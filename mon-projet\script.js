const modules = [
    { name: "Modelisation et Simulation avansee", coef: 2, td: false, tp: true },
    { name: "<PERSON><PERSON><PERSON>", coef: 1, td: false, tp: false },
    { name: "Text et web mining", coef: 3, td: true, tp: true },
    { name: "AID", coef: 2, td: false, tp: true },
    { name: "syberSecurite", coef: 2, td: true, tp: false },
    { name: "Sociologie de l'internet", coef: 2, td: false, tp: true },
    { name: "informatique legale et tic", coef: 2, td: true, tp: false },
    { name: "Partenariat", coef: 1, td: false, tp: false },
    { name: "Genie Logiciel", coef: 2, td: true, tp: false }
];

function createModuleInputs() {
    const container = document.getElementById('modulesContainer');

    // Créer la table
    const table = document.createElement('table');
    table.className = 'notes-table';

    // En-tête du tableau
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Module</th>
            <th>Coef</th>
            <th>Examen (/20)</th>
            <th>TD (/20)</th>
            <th>TP (/20)</th>
            <th>Moyenne Module</th>
            <th>Moyenne × Coef</th>
        </tr>
    `;
    table.appendChild(thead);

    // Corps du tableau
    const tbody = document.createElement('tbody');

    modules.forEach((module, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="module-name">${module.name}</td>
            <td class="coef">${module.coef}</td>
            <td class="grade-input">
                <input type="number" min="0" max="20" step="0.01" id="exam-${index}" placeholder="--">
            </td>
            <td class="grade-input">
                ${module.td ? `<input type="number" min="0" max="20" step="0.01" id="td-${index}" placeholder="--">` : '<span class="not-applicable">--</span>'}
            </td>
            <td class="grade-input">
                ${module.tp ? `<input type="number" min="0" max="20" step="0.01" id="tp-${index}" placeholder="--">` : '<span class="not-applicable">--</span>'}
            </td>
            <td class="module-avg" id="module-avg-${index}">--</td>
            <td class="weighted-avg" id="module-average-${index}">0.00</td>
        `;

        // Ajout des écouteurs d'événements
        const inputs = row.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => updateModuleAverage(index));
        });

        tbody.appendChild(row);
    });

    table.appendChild(tbody);

    // Ajouter une ligne de total
    const tfoot = document.createElement('tfoot');
    tfoot.innerHTML = `
        <tr class="total-row">
            <td colspan="5" class="total-label">MOYENNE GÉNÉRALE DU SEMESTRE</td>
            <td id="general-average" class="general-average">--</td>
            <td id="total-weighted" class="total-weighted">--</td>
        </tr>
        <tr class="coef-total-row">
            <td colspan="5" class="total-label">TOTAL COEFFICIENTS</td>
            <td id="total-coef" class="total-coef">${modules.reduce((sum, module) => sum + module.coef, 0)}</td>
            <td class="mention" id="mention">--</td>
        </tr>
    `;
    table.appendChild(tfoot);

    container.appendChild(table);
}

function updateModuleAverage(index) {
    const module = modules[index];
    const examInput = document.getElementById(`exam-${index}`);
    const exam = parseFloat(examInput.value) || 0;
    let activityNote = 0;
    let hasActivity = module.td || module.tp;
    let moduleAvg = 0;

    if (hasActivity) {
        if (module.td && module.tp) {
            const td = parseFloat(document.getElementById(`td-${index}`).value) || 0;
            const tp = parseFloat(document.getElementById(`tp-${index}`).value) || 0;
            activityNote = (td + tp) / 2;
        } else if (module.td) {
            activityNote = parseFloat(document.getElementById(`td-${index}`).value) || 0;
        } else if (module.tp) {
            activityNote = parseFloat(document.getElementById(`tp-${index}`).value) || 0;
        }
        moduleAvg = (exam + activityNote) / 2;
    } else {
        moduleAvg = exam;
    }

    const weightedAverage = moduleAvg * module.coef;

    // Afficher la moyenne du module
    const moduleAvgCell = document.getElementById(`module-avg-${index}`);
    moduleAvgCell.textContent = examInput.value ? moduleAvg.toFixed(2) : '--';

    // Afficher la moyenne pondérée
    document.getElementById(`module-average-${index}`).textContent = examInput.value ? weightedAverage.toFixed(2) : '0.00';

    // Mettre à jour la moyenne générale en temps réel
    updateGeneralAverage();
}

function updateGeneralAverage() {
    let totalWeighted = 0;
    let totalCoef = 0;
    let hasValidGrades = false;

    modules.forEach((module, index) => {
        const examInput = document.getElementById(`exam-${index}`);
        if (examInput.value) {
            const weightedAvg = parseFloat(document.getElementById(`module-average-${index}`).textContent) || 0;
            totalWeighted += weightedAvg;
            totalCoef += module.coef;
            hasValidGrades = true;
        }
    });

    const generalAverage = hasValidGrades ? totalWeighted / totalCoef : 0;

    // Mettre à jour l'affichage
    document.getElementById('general-average').textContent = hasValidGrades ? generalAverage.toFixed(2) : '--';
    document.getElementById('total-weighted').textContent = hasValidGrades ? totalWeighted.toFixed(2) : '--';

    // Déterminer la mention
    let mention = '--';
    if (hasValidGrades) {
        if (generalAverage >= 16) mention = 'Très Bien';
        else if (generalAverage >= 14) mention = 'Bien';
        else if (generalAverage >= 12) mention = 'Assez Bien';
        else if (generalAverage >= 10) mention = 'Passable';
        else mention = 'Ajourné';
    }

    document.getElementById('mention').textContent = mention;

    // Afficher le message selon la moyenne (sans alerte)
    if (hasValidGrades) {
        updateResultMessage(generalAverage);
    }
}

function updateResultMessage(finalAverage) {
    const resultDiv = document.getElementById('result');
    if (finalAverage < 10) {
        resultDiv.innerHTML = `💪 ZA3IM W RADJAL 💪 || 👉🏼 ${finalAverage.toFixed(2)}/20 👈🏼`;
        resultDiv.style.color = '#d32f2f';
    } else {
        resultDiv.innerHTML = `👹 bsa7tk wach ra7 n9olk 👹 || 👉🏼 ${finalAverage.toFixed(2)}/20 👈🏼`;
        resultDiv.style.color = '#388e3c';
    }
}

// Fonction conservée pour compatibilité mais plus nécessaire
function calculateAverage() {
    // La moyenne est maintenant calculée en temps réel
    updateGeneralAverage();
}

// Initialisation
createModuleInputs();