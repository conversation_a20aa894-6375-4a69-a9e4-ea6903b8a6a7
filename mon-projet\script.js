const modules = [
    { name: "Modelisation et Simulation avansee", coef: 2, td: true, tp: false },
    { name: "<PERSON><PERSON><PERSON>", coef: 1, td: false, tp: false },
    { name: "Text et web mining", coef: 3, td: true, tp: true },
    { name: "AID", coef: 2, td: false, tp: true },
    { name: "syberSecurite", coef: 2, td: false, tp: true },
    { name: "Sociologie de l'internet", coef: 2, td: false, tp: true },
    { name: "informatique legale et tic", coef: 2, td: false, tp: false },
    { name: "Partenariat", coef: 1, td: false, tp: false },
    { name: "Genie Logiciel", coef: 2, td: true, tp: false }
];

function createModuleInputs() {
    const container = document.getElementById('modulesContainer');

    modules.forEach((module, index) => {
        const card = document.createElement('div');
        card.className = 'module-card';
        card.innerHTML = `
            <div class="module-title">${module.name} (Coef: ${module.coef})</div>
            <div class="input-group">
                <label>Examen (/20)</label>
                <input type="number" min="0" max="20" step="0.01" id="exam-${index}" required>
            </div>
            ${module.td ? `
            <div class="input-group">
                <label>TD (/20)</label>
                <input type="number" min="0" max="20" step="0.01" id="td-${index}">
            </div>` : ''}
            ${module.tp ? `
            <div class="input-group">
                <label>TP (/20)</label>
                <input type="number" min="0" max="20" step="0.01" id="tp-${index}">
            </div>` : ''}
            <div class="module-average">Moyenne module * coef : <span id="module-average-${index}">0.00</span></div>
        `;

        // Ajout des écouteurs d'événements
        const inputs = card.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => updateModuleAverage(index));
        });

        container.appendChild(card);
    });
}

function updateModuleAverage(index) {
    const module = modules[index];
    const exam = parseFloat(document.getElementById(`exam-${index}`).value) || 0;
    let activityNote = 0;
    let hasActivity = module.td || module.tp;

    if (hasActivity) {
        if (module.td && module.tp) {
            const td = parseFloat(document.getElementById(`td-${index}`).value) || 0;
            const tp = parseFloat(document.getElementById(`tp-${index}`).value) || 0;
            activityNote = (td + tp) / 2;
        } else if (module.td) {
            activityNote = parseFloat(document.getElementById(`td-${index}`).value) || 0;
        } else if (module.tp) {
            activityNote = parseFloat(document.getElementById(`tp-${index}`).value) || 0;
        }
    }

    const moduleAverage = hasActivity
        ? ((exam + activityNote) / 2) * module.coef
        : exam * module.coef;

    document.getElementById(`module-average-${index}`).textContent = moduleAverage.toFixed(2);
}

function calculateAverage() {
    let total = 0;
    let totalCoef = 0;

    modules.forEach((_, index) => {
        const average = parseFloat(document.getElementById(`module-average-${index}`).textContent);
        total += average;
        totalCoef += modules[index].coef;
    });

    const finalAverage = total / totalCoef;
    document.getElementById('result').innerHTML = `
        Moyenne du semestre : ${finalAverage.toFixed(2)}/20
    `;

    // Afficher le message en fonction de la moyenne
    showMessage(finalAverage);
}

function showMessage(finalAverage) {
    const alertMessage = document.getElementById('alertMessage');
    const customAlert = document.getElementById('customAlert');
      if(finalAverage < 10){
    alertMessage.textContent = `💪 ZA3IM W RADJAL 💪 || 👉🏼 ${finalAverage.toFixed(2)}/20 👈🏼`;
    customAlert.style.display = 'block';
      }else{
        alertMessage.textContent = `👹 bsa7tk wach ra7 n9olk 👹 || 👉🏼 ${finalAverage.toFixed(2)}/20 👈🏼`;
        customAlert.style.display = 'block';
      }
}

function closeCustomAlert() {
    const customAlert = document.getElementById('customAlert');
    customAlert.style.display = 'none';
}

// Initialisation
createModuleInputs();