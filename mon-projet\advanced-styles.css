/* ===== STYLES AVANCÉS POUR LE CALCULATEUR BTK ===== */

/* En-tête avec statistiques */
.app-header {
    text-align: center;
    margin: 2rem 0;
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.app-header h1 {
    color: var(--text);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-bar {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    font-family: 'JetBrains Mono', monospace;
}

/* Barre de progression */
.progress-container {
    margin: 2rem 0;
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text);
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--bg-tertiary);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 6px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Panneau de contrôles */
.controls-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    background: var(--bg-primary);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-btn:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-1px);
}

.filter-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.view-controls {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.2rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    background: var(--accent);
    color: white;
    transform: translateY(-1px);
}

.view-btn.active {
    background: var(--accent);
    color: white;
    border-color: var(--accent);
}

/* Vue en cartes */
.modules-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.module-card-view {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.module-card-view:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.card-header {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.card-coef {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.card-body {
    padding: 1rem;
}

.grade-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.grade-row label {
    font-weight: 500;
    color: var(--text);
    font-size: 0.875rem;
}

.grade-row input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
    background: var(--bg-secondary);
    color: var(--text);
}

.card-footer {
    background: var(--bg-secondary);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
}

.card-average, .card-weighted {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.avg-value, .weighted-value {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 600;
    color: var(--primary);
}

/* Boutons améliorés */
.validation-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.validate-btn, .clear-btn, .save-btn, .load-btn, .pdf-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.validate-btn {
    background: var(--success);
    color: white;
}

.clear-btn {
    background: var(--error);
    color: white;
}

.save-btn {
    background: var(--info);
    color: white;
}

.load-btn {
    background: var(--warning);
    color: white;
}

.pdf-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    position: relative;
    overflow: hidden;
}

.pdf-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.pdf-btn:hover::before {
    left: 100%;
}

.validate-btn:hover, .clear-btn:hover, .save-btn:hover, .load-btn:hover, .pdf-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-icon {
    font-size: 1rem;
}

.btn-text {
    font-size: 0.875rem;
}

/* Sélecteur de semestre amélioré */
.semester-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.semester-icon {
    font-size: 1.5rem;
}

.semester-text {
    font-weight: 600;
    font-size: 0.875rem;
}

.semester-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--accent);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-family: 'JetBrains Mono', monospace;
}

/* Modales */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h2 {
    margin: 0;
    color: var(--text);
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text);
}

.modal-body {
    padding: 1.5rem;
}

.help-section {
    margin-bottom: 2rem;
}

.help-section h3 {
    color: var(--primary);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.help-section ul {
    list-style: none;
    padding: 0;
}

.help-section li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text);
}

.help-section li:last-child {
    border-bottom: none;
}

/* Formulaire dans la modal */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text);
    font-size: 0.875rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    background: var(--bg-secondary);
    color: var(--text);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.pdf-generate-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.pdf-generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.cancel-btn {
    background: var(--bg-tertiary);
    color: var(--text);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.cancel-btn:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
}

/* Modal de bienvenue */
.welcome-content {
    text-align: center;
}

.welcome-content p {
    margin-bottom: 1.5rem;
    color: var(--text);
    line-height: 1.6;
}

.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    background: var(--bg-secondary);
    color: var(--text);
    transition: var(--transition);
}

.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.privacy-notice {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 6px;
    margin: 1.5rem 0;
    border-left: 3px solid var(--info);
}

.privacy-notice p {
    margin: 0;
    color: var(--text-light);
    font-style: italic;
}

.welcome-btn {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.welcome-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.skip-btn {
    background: transparent;
    color: var(--text-light);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.skip-btn:hover {
    background: var(--bg-secondary);
    color: var(--text);
    transform: translateY(-1px);
}

/* Statistiques des visiteurs */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stat-card h3 {
    margin: 0 0 1rem 0;
    color: var(--primary);
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--primary);
    padding-bottom: 0.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text);
    font-weight: 500;
    font-size: 0.875rem;
}

.stat-value {
    color: var(--primary);
    font-weight: 700;
    font-family: 'JetBrains Mono', monospace;
    font-size: 1rem;
}

.stats-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.export-stats-btn {
    background: linear-gradient(135deg, var(--info), #0056b3);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.export-stats-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.clear-stats-btn {
    background: linear-gradient(135deg, var(--error), #c82333);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.clear-stats-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Notifications Toast */
.toast-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: var(--bg-primary);
    border-radius: 8px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    min-width: 300px;
    opacity: 1;
    transition: opacity 0.3s ease;
    animation: slideInRight 0.3s ease;
}

.toast-success {
    border-left: 4px solid var(--success);
}

.toast-error {
    border-left: 4px solid var(--error);
}

.toast-warning {
    border-left: 4px solid var(--warning);
}

.toast-info {
    border-left: 4px solid var(--info);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

.toast-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.toast-message {
    flex: 1;
    color: var(--text);
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-light);
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
}

.toast-close:hover {
    background: var(--bg-secondary);
    color: var(--text);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Résultat amélioré */
.result-container {
    margin: 2rem 0;
}

.result {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    padding: 2rem;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text);
    transition: var(--transition);
}

.result-success {
    border-left: 5px solid var(--success);
    background: linear-gradient(135deg, var(--bg-primary), rgba(40, 167, 69, 0.05));
}

.result-warning {
    border-left: 5px solid var(--error);
    background: linear-gradient(135deg, var(--bg-primary), rgba(220, 53, 69, 0.05));
}

.result-main {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.result-score {
    font-size: 2rem;
    margin: 1rem 0;
    font-family: 'JetBrains Mono', monospace;
    font-weight: 700;
}

.result-progress {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 500;
    margin-top: 1rem;
}

.result-details {
    margin-top: 1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    padding: 1rem;
    display: none;
}