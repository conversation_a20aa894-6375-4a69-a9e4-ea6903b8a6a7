<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur de moyenne - Master 1 TIC</title>
    <link rel="stylesheet" href="./mon-projet/style.css">
    <link rel="stylesheet" href="./mon-projet/advanced-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎓</text></svg>">

    <!-- Bibliothèques pour l'export PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="navbar">
        <div class="nav-brand">
            <span class="nav-icon">🎓</span>
            <span class="nav-title">BTK Calculator</span>
        </div>
        <div class="nav-actions">
            <button class="nav-btn" onclick="toggleTheme()" title="Changer le thème">
                <span id="theme-icon">🌙</span>
            </button>
            <button class="nav-btn" onclick="exportToPDF()" title="Exporter en PDF">
                📄
            </button>
            <button class="nav-btn" onclick="exportData()" title="Exporter les données JSON">
                📊
            </button>
            <button class="nav-btn" onclick="showHelp()" title="Aide">
                ❓
            </button>
            <button class="nav-btn" onclick="showAdminLogin()" title="Accès Admin - Statistiques">
                🔐
            </button>
        </div>
    </nav>

    <div class="container">
        <!-- En-tête avec statistiques -->
        <header class="app-header">
            <h1>BTK-Calculateur de moyenne - Master 1 TIC S2</h1>
            <div class="stats-bar" id="statsBar">
                <div class="stat-item">
                    <span class="stat-label">Modules</span>
                    <span class="stat-value" id="totalModules">9</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Coefficients</span>
                    <span class="stat-value" id="totalCoef">17</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Progression</span>
                    <span class="stat-value" id="progressPercent">0%</span>
                </div>
            </div>
        </header>

        <!-- Sélecteur de semestre amélioré -->
        <div class="semester-selector">
            <h3>Choisir le semestre :</h3>
            <div class="semester-buttons">
                <button class="semester-btn" data-semester="1" onclick="changeSemester(1)">
                    <span class="semester-icon">📚</span>
                    <span class="semester-text">Semestre 1</span>
                    <span class="semester-badge" id="s1-badge">0/9</span>
                </button>
                <button class="semester-btn active" data-semester="2" onclick="changeSemester(2)">
                    <span class="semester-icon">📖</span>
                    <span class="semester-text">Semestre 2</span>
                    <span class="semester-badge" id="s2-badge">0/9</span>
                </button>
            </div>
        </div>

        <!-- Barre de progression -->
        <div class="progress-container">
            <div class="progress-label">
                <span>Progression de saisie</span>
                <span id="progressText">0/9 modules</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Filtres et options -->
        <div class="controls-panel">
            <div class="filter-controls">
                <button class="filter-btn active" onclick="filterModules('all')" data-filter="all">
                    Tous les modules
                </button>
                <button class="filter-btn" onclick="filterModules('completed')" data-filter="completed">
                    Complétés
                </button>
                <button class="filter-btn" onclick="filterModules('incomplete')" data-filter="incomplete">
                    À compléter
                </button>
            </div>
            <div class="view-controls">
                <button class="view-btn active" onclick="changeView('table')" data-view="table" title="Vue tableau">
                    📋
                </button>
                <button class="view-btn" onclick="changeView('cards')" data-view="cards" title="Vue cartes">
                    🗃️
                </button>
            </div>
        </div>

        <div class="modules-grid" id="modulesContainer"></div>

        <!-- Contrôles de validation améliorés -->
        <div class="validation-controls">
            <button type="button" onclick="validateAllInputs()" class="validate-btn">
                <span class="btn-icon">🔍</span>
                <span class="btn-text">Vérifier toutes les notes</span>
            </button>
            <button type="button" onclick="clearAllInputs()" class="clear-btn">
                <span class="btn-icon">🗑️</span>
                <span class="btn-text">Effacer tout</span>
            </button>
            <button type="button" onclick="saveData()" class="save-btn">
                <span class="btn-icon">💾</span>
                <span class="btn-text">Sauvegarder</span>
            </button>
            <button type="button" onclick="loadData()" class="load-btn">
                <span class="btn-icon">📂</span>
                <span class="btn-text">Charger</span>
            </button>
            <button type="button" onclick="exportToPDF()" class="pdf-btn">
                <span class="btn-icon">📄</span>
                <span class="btn-text">Export PDF</span>
            </button>
        </div>

        <!-- Résultat amélioré -->
        <div class="result-container">
            <div class="result" id="result"></div>
            <div class="result-details" id="resultDetails"></div>
        </div>
    </div>

    <!-- Modal d'aide -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Guide d'utilisation</h2>
                <button class="modal-close" onclick="closeModal('helpModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <h3>🎯 Comment utiliser le calculateur</h3>
                    <ul>
                        <li>Sélectionnez votre semestre (1 ou 2)</li>
                        <li>Saisissez vos notes dans les champs correspondants</li>
                        <li>La moyenne se calcule automatiquement en temps réel</li>
                        <li>Utilisez les boutons de validation pour vérifier vos saisies</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h3>📊 Système de notation</h3>
                    <ul>
                        <li><strong>Très Bien:</strong> ≥ 16/20</li>
                        <li><strong>Bien:</strong> 14-15.99/20</li>
                        <li><strong>Assez Bien:</strong> 12-13.99/20</li>
                        <li><strong>Passable:</strong> 10-11.99/20</li>
                        <li><strong>Ajourné:</strong> < 10/20</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h3>💾 Fonctionnalités</h3>
                    <ul>
                        <li>Sauvegarde automatique des données</li>
                        <li>Export des résultats en PDF</li>
                        <li>Mode sombre/clair</li>
                        <li>Interface responsive</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de bienvenue pour nouveaux visiteurs -->
    <div class="modal" id="welcomeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎓 Bienvenue sur BTK Calculator!</h2>
                <button class="modal-close" onclick="closeWelcomeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="welcome-content">
                    <p>Merci de visiter notre calculateur de moyenne pour Master 1 TIC!</p>
                    <p>Pour améliorer votre expérience, nous aimerions connaître quelques informations (optionnelles) :</p>

                    <form id="visitorForm">
                        <div class="form-group">
                            <label for="visitorName">Nom (optionnel) :</label>
                            <input type="text" id="visitorName" placeholder="Votre nom">
                        </div>
                        <div class="form-group">
                            <label for="visitorUniversity">Université (optionnel) :</label>
                            <input type="text" id="visitorUniversity" placeholder="Votre université">
                        </div>
                        <div class="form-group">
                            <label for="visitorLevel">Niveau d'études :</label>
                            <select id="visitorLevel">
                                <option value="">-- Sélectionner --</option>
                                <option value="Master 1 TIC">Master 1 TIC</option>
                                <option value="Master 2 TIC">Master 2 TIC</option>
                                <option value="Licence">Licence</option>
                                <option value="Autre Master">Autre Master</option>
                                <option value="Enseignant">Enseignant</option>
                                <option value="Autre">Autre</option>
                            </select>
                        </div>
                        <div class="privacy-notice">
                            <p><small>🔒 Ces informations sont stockées localement et anonymement pour des statistiques d'usage. Aucune donnée personnelle n'est transmise à des tiers.</small></p>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="registerVisitor()" class="welcome-btn">
                                ✅ Continuer
                            </button>
                            <button type="button" onclick="skipRegistration()" class="skip-btn">
                                ⏭️ Passer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de connexion admin -->
    <div class="modal" id="adminLoginModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🔐 Accès Administrateur</h2>
                <button class="modal-close" onclick="closeModal('adminLoginModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="admin-login-content">
                    <p>Accès restreint aux statistiques des visiteurs</p>

                    <form id="adminLoginForm">
                        <div class="form-group">
                            <label for="adminUsername">Nom d'utilisateur :</label>
                            <input type="text" id="adminUsername" placeholder="Entrez votre nom d'utilisateur" required>
                        </div>
                        <div class="form-group">
                            <label for="adminPassword">Mot de passe :</label>
                            <input type="password" id="adminPassword" placeholder="Entrez votre mot de passe" required>
                        </div>
                        <div id="loginError" class="login-error" style="display: none;">
                            ❌ Identifiants incorrects
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="attemptAdminLogin()" class="admin-login-btn">
                                🔓 Se connecter
                            </button>
                            <button type="button" onclick="closeModal('adminLoginModal')" class="cancel-btn">
                                Annuler
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal des statistiques des visiteurs -->
    <div class="modal" id="visitorStatsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📈 Statistiques des Visiteurs</h2>
                <button class="modal-close" onclick="closeModal('visitorStatsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="visitorStatsContent">
                    <!-- Contenu généré dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour les informations étudiant -->
    <div class="modal" id="studentInfoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Informations Étudiant</h2>
                <button class="modal-close" onclick="closeModal('studentInfoModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="studentInfoForm">
                    <div class="form-group">
                        <label for="studentName">Nom :</label>
                        <input type="text" id="studentName" placeholder="Entrez votre nom">
                    </div>
                    <div class="form-group">
                        <label for="studentFirstName">Prénom :</label>
                        <input type="text" id="studentFirstName" placeholder="Entrez votre prénom">
                    </div>
                    <div class="form-group">
                        <label for="studentId">Matricule :</label>
                        <input type="text" id="studentId" placeholder="Entrez votre matricule">
                    </div>
                    <div class="form-group">
                        <label for="university">Université :</label>
                        <input type="text" id="university" placeholder="Nom de l'université" value="UNIVERSITÉ - FACULTÉ">
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="generatePDFWithInfo()" class="pdf-generate-btn">
                            📄 Générer le PDF
                        </button>
                        <button type="button" onclick="closeModal('studentInfoModal')" class="cancel-btn">
                            Annuler
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="./mon-projet/script.js"></script>
</body>
</html>