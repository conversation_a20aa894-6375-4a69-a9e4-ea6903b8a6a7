:root {
    --primary: #667eea;
    --primary-dark: #5a67d8;
    --secondary: #f7fafc;
    --accent: #48bb78;
    --accent-light: #68d391;
    --text: #2d3748;
    --text-light: #4a5568;
    --white: #ffffff;
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
    background: #f8f9fa;
    min-height: 100vh;
    color: var(--text);
    padding: 20px;
    font-family: 'Times New Roman', serif;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    padding: 40px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 8px;
}

/* En-tête officiel */
.container::before {
    content: "RÉPUBLIQUE ALGÉRIENNE DÉMOCRATIQUE ET POPULAIRE\AMINISTÈRE DE L'ENSEIGNEMENT SUPÉRIEUR ET DE LA RECHERCHE SCIENTIFIQUE\ARELEVÉ DE NOTES - SEMESTRE 2";
    display: block;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: #333;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #333;
    line-height: 1.6;
    white-space: pre-line;
}

h1 {
    text-align: center;
    margin: 20px 0 40px 0;
    color: #333;
    font-size: 1.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

p {
    text-align: center;
    margin: 20px 0;
    color: #333;
    font-size: 1rem;
    font-weight: normal;
}

.module-average {
    margin-top: 15px;
    font-weight: 600;
    color: var(--accent);
    font-size: 1.1em;
    padding: 8px 12px;
    background: rgba(72, 187, 120, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--accent);
}

/* Style de table officielle pour relevé de notes */
.notes-table {
    width: 100%;
    border-collapse: collapse;
    margin: 30px 0;
    font-family: 'Times New Roman', serif;
    font-size: 14px;
    border: 2px solid #000;
}

.notes-table th {
    background-color: #f0f0f0;
    border: 1px solid #000;
    padding: 12px 8px;
    text-align: center;
    font-weight: bold;
    font-size: 13px;
    text-transform: uppercase;
}

.notes-table td {
    border: 1px solid #000;
    padding: 10px 8px;
    text-align: center;
    vertical-align: middle;
}

.notes-table .module-name {
    text-align: left;
    font-weight: 500;
    padding-left: 12px;
    max-width: 200px;
}

.notes-table .coef {
    font-weight: bold;
    background-color: #f9f9f9;
}

.notes-table .grade-input input {
    width: 60px;
    border: 1px solid #ccc;
    padding: 4px;
    text-align: center;
    font-size: 13px;
    border-radius: 2px;
}

.notes-table .grade-input input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 3px rgba(0, 123, 255, 0.3);
}

/* États de validation des inputs */
.notes-table .grade-input input.error {
    border-color: #dc3545;
    background-color: #fff5f5;
    box-shadow: 0 0 3px rgba(220, 53, 69, 0.3);
}

.notes-table .grade-input input.warning {
    border-color: #ffc107;
    background-color: #fffbf0;
    box-shadow: 0 0 3px rgba(255, 193, 7, 0.3);
}

.notes-table .grade-input input:valid {
    border-color: #28a745;
    background-color: #f8fff8;
}

/* Messages de validation */
.validation-message {
    position: absolute;
    z-index: 1000;
    background: #fff;
    border: 1px solid;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 11px;
    font-weight: 500;
    margin-top: 2px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    max-width: 200px;
    word-wrap: break-word;
}

.validation-message.error {
    border-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

.validation-message.warning {
    border-color: #ffc107;
    background-color: #fff3cd;
    color: #856404;
}

/* Position relative pour les cellules d'input */
.notes-table .grade-input {
    position: relative;
}

/* Résumé de validation */
.validation-summary {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 8px;
    margin: 20px 0;
    padding: 0;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
    animation: slideDown 0.3s ease-out;
}

.summary-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 12px;
}

.summary-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.summary-text {
    flex: 1;
    font-weight: 500;
    color: #856404;
    font-size: 14px;
}

.summary-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #856404;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.summary-close:hover {
    background-color: rgba(133, 100, 4, 0.1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Boutons de contrôle de validation */
.validation-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 25px 0;
    flex-wrap: wrap;
}

.validate-btn,
.clear-btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Times New Roman', serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.validate-btn {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.validate-btn:hover {
    background: white;
    color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.clear-btn {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.clear-btn:hover {
    background: white;
    color: #dc3545;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.notes-table .not-applicable {
    color: #999;
    font-style: italic;
}

.notes-table .module-avg,
.notes-table .weighted-avg {
    font-weight: bold;
    background-color: #f9f9f9;
}

.notes-table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.notes-table tbody tr:hover {
    background-color: #f0f8ff;
}

/* Styles pour les lignes de total */
.notes-table tfoot {
    border-top: 3px solid #000;
}

.notes-table .total-row,
.notes-table .coef-total-row {
    background-color: #e8f4f8;
    font-weight: bold;
}

.notes-table .total-label {
    text-align: right;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding-right: 15px;
}

.notes-table .general-average {
    background-color: #d4edda;
    color: #155724;
    font-size: 16px;
    font-weight: bold;
}

.notes-table .total-weighted {
    background-color: #f8f9fa;
    font-weight: bold;
}

.notes-table .total-coef {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
}

.notes-table .mention {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}



/* Résultat style officiel */
.result {
    text-align: center;
    font-size: 1.8em;
    font-weight: bold;
    padding: 25px;
    background: #f8f9fa;
    border: 3px solid #333;
    max-width: 600px;
    margin: 30px auto;
    font-family: 'Times New Roman', serif;
    color: #333;
}

.result::before {
    content: "RÉSULTAT OFFICIEL";
    display: block;
    font-size: 0.6em;
    font-weight: normal;
    margin-bottom: 15px;
    letter-spacing: 2px;
    color: #666;
}



/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 20px;
    }

    .container::before {
        font-size: 10px;
        line-height: 1.4;
    }

    h1 {
        font-size: 1.4rem;
        margin: 15px 0 25px 0;
    }

    .notes-table {
        font-size: 12px;
    }

    .notes-table th,
    .notes-table td {
        padding: 8px 4px;
    }

    .notes-table .module-name {
        font-size: 11px;
        padding-left: 6px;
    }

    .notes-table .grade-input input {
        width: 50px;
        font-size: 11px;
    }



    .result {
        font-size: 1.4em;
        padding: 20px;
        margin: 20px auto;
    }
}

@media (max-width: 480px) {
    .notes-table {
        font-size: 10px;
    }

    .notes-table th,
    .notes-table td {
        padding: 6px 2px;
    }

    .notes-table .grade-input input {
        width: 40px;
        font-size: 10px;
    }

    h1 {
        font-size: 1.2rem;
    }

    .result {
        font-size: 1.2em;
    }
}