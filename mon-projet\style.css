:root {
    --primary: #667eea;
    --primary-dark: #5a67d8;
    --secondary: #f7fafc;
    --accent: #48bb78;
    --accent-light: #68d391;
    --text: #2d3748;
    --text-light: #4a5568;
    --white: #ffffff;
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text);
    padding: 20px;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

h1 {
    text-align: center;
    margin: 40px 0;
    color: var(--white);
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.025em;
}

p {
    text-align: center;
    margin: 30px 0;
    color: var(--white);
    font-size: 1.25rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.module-average {
    margin-top: 15px;
    font-weight: 600;
    color: var(--accent);
    font-size: 1.1em;
    padding: 8px 12px;
    background: rgba(72, 187, 120, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--accent);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.module-card {
    background: var(--white);
    padding: 24px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
}

.module-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.module-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--primary);
}

.input-group {
    margin-bottom: 12px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9em;
}

input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
}

button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 1.1em;
    cursor: pointer;
    transition: 0.3s;
    display: block;
    margin: 20px auto;
}

button:hover {
    opacity: 0.9;
}

.result {
    text-align: center;
    font-size: 1.5em;
    font-weight: bold;
    padding: 20px;
    background: white;
    border-radius: 10px;
    max-width: 500px;
    margin: 20px auto;
}

/* Styles pour l'alerte personnalisée */
.custom-alert {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.custom-alert-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 400px;
    border-radius: 8px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.custom-button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
    transition: 0.3s;
}

.custom-button:hover {
    opacity: 0.9;
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }

    .module-card {
        padding: 15px;
    }
}